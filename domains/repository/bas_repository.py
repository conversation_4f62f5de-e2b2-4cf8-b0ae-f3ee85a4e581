#!/usr/bin/env python3

from datetime import datetime
from typing import Any, Dict, List
from facades.baservice.basdb import BasDbFacade, table_name
from lib.querybuilder import QueryBuilder


class BasRepository:

    def __init__(self, bas_conn: BasDbFacade):
        self._bas_conn = bas_conn
    #def

    @staticmethod
    def new_instance():
        return BasRepository(BasDbFacade.instance())
    #

    def get_amazon_product_order_history(self, customer_id: int, asin: str, sku: str, marketplace_id: str,  start_date: datetime, end_date: datetime,  limit: int = 1000000, offset: int = 0) -> List[Dict[str, Any]]:
        """
        Get Amazon order history for a product.
        
        Args:
            customer_id: Customer ID
            asin: Product ASIN
            sku: Product SKU
            marketplace_id: Marketplace ID
            start_date: Start date for orders
            end_date: End date for orders
            limit: Maximum number of records to return
            offset: Offset for pagination
        
        Returns:
            List[Dict[str, Any]]: with order history
        """
        builder = QueryBuilder('clickhouse')
        builder.table(table_name(customer_id, 'amazon_order')) \
               .select(
                    'id',
                    'order_purchase_date',
                    'asin',
                    'sku',
                    'seller_id',
                    'title',
                    'order_marketplace_id',
                    'quantity',
                    'item_price',
                    'profit') \
               .where("quantity", ">", 0) \
               .where("item_price", ">", 0) \
               .where("order_status", "=", "Shipped") \
               .where("order_purchase_date", ">=", start_date.strftime('%Y-%m-%d')) \
               .where("order_purchase_date", "<", end_date.strftime('%Y-%m-%d')) \
               .order_by("order_purchase_date", "ASC") \
               .offset(offset) \
               .limit(limit)

            # 'asin',
            # 'sku',
            # 'order_marketplace_id',
            # 'COUNT(id) AS total_orders',
            # 'SUM(quantity) AS total_quantity',
            # 'SUM(item_price) AS total_price') \
            # .group_by('order_purchase_date', 'asin', 'sku', 'order_marketplace_id') \
        
        builder.where("asin", "=", asin) \
               .where("sku", "=", sku) \
               .where("order_marketplace_id", "=", marketplace_id) \

        # Get the SQL query
        query = builder.get_raw_sql()
        # print('get_amazon_order_history: sql=', query)
        # Execute the query
        return list(self._bas_conn.client_db_query(customer_id).query_all(query))
    #def


    def get_all_order_history(self, customer_id: int, start_date: datetime, end_date: datetime, limit: int = 1000000, offset: int = 0) -> List[Dict[str, Any]]:
        """
        Get Amazon order history with optional filtering by order_keys.
        
        Args:
            customer_id: Customer ID
            start_from_date: Start date for orders
            order_keys: Optional list of [asin, sku, marketplace_id, seller_id] order_keys for filtering
        
        Returns:
            List[Dict[str, Any]]: with order history
        """
                
        builder = QueryBuilder('clickhouse')
        builder.table(table_name(customer_id, 'amazon_order')) \
               .select('toDate(order_purchase_date) as order_purchase_date',
                    'asin',
                    'sku',
                    'order_marketplace_id',
                    'SUM(quantity) AS total_quantity',
                    'SUM(item_price) AS total_price') \
               .where("quantity", ">", 0) \
               .where("item_price", ">", 0) \
               .where("order_status", "=", "Shipped") \
               .where("order_purchase_date", ">=", start_date.strftime('%Y-%m-%d')) \
               .where("order_purchase_date", "<", end_date.strftime('%Y-%m-%d')) \
               .order_by("asin", "ASC") \
               .order_by("sku", "ASC") \
               .order_by("order_marketplace_id", "ASC") \
               .order_by("order_purchase_date", "ASC") \
               .group_by('order_purchase_date', 'asin', 'sku', 'order_marketplace_id') \
               .offset(offset) \
               .limit(limit)
        
        # Get the SQL query
        query = builder.get_raw_sql()
        # print('get_all_order_history: sql=', query)
        # Execute the query
        rows = list(self._bas_conn.client_db_query(customer_id).query_all(query))

        result = []
        for row in rows:
            row['customer_id'] = customer_id
            result.append(row)

        return result

    #def
#class




# SELECT toDate(order_purchase_date) as order_purchase_date,
#        asin,
#        sku,
#        order_marketplace_id,
#        COUNT(id) AS total_orders,
#        SUM(quantity) AS total_quantity,
#        SUM(item_price) AS total_price,
#        lagInFrame(SUM(quantity)) OVER (PARTITION BY asin, sku, order_marketplace_id ORDER BY order_purchase_date ASC) AS prev_total_quantity,
#        lagInFrame(SUM(item_price)) OVER (PARTITION BY asin, sku, order_marketplace_id ORDER BY order_purchase_date ASC) AS prev_total_price,
#        total_price / total_quantity as price_per_quantity,
#        prev_total_price / prev_total_quantity as prev_price_per_quantity
# FROM customer_09766.amazon_order
# WHERE quantity > 0
#   AND item_price > 0
#   AND order_status = 'Shipped'
#   AND order_purchase_date >= '2025-05-20'
#   AND order_purchase_date < '2025-06-06'
# --  AND (asin, sku, order_marketplace_id, seller_id) IN (('B0B1J4KLWD', 'WS-ED-CR', 'A1PA6795UKMFR9', 'A2IBEVL9WPD7U7'))
#   and  (asin, sku, order_marketplace_id, seller_id) IN (('B09H33Y847', 'GM-GU-SW-50_50', 'A1PA6795UKMFR9', 'A2IBEVL9WPD7U7'))

# GROUP BY order_purchase_date, asin, sku, order_marketplace_id
# ORDER BY order_purchase_date ASC
# LIMIT 0, 1000000

