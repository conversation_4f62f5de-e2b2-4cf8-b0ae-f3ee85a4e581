"""
Product Category Management System

This module provides functionality for managing product categories and their associated tags.
It loads category data from external files and provides utilities for category operations.
"""
import os
from typing import List, Dict

def _get_data_file_path(filename: str) -> str:
    """Get the full path to a data file in the same directory as this module."""
    current_dir = os.path.dirname(os.path.abspath(__file__))
    return os.path.join(current_dir, filename)


# def load_categories_from_file() -> List[str]:
#     """
#     Load category names from categories.txt file.

#     Returns:
#         List of category names
#     """
#     categories = []
#     try:
#         with open(_get_data_file_path('categories.txt'), 'r', encoding='utf-8') as f:
#             for line in f:
#                 line = line.strip()
#                 if line:  # Skip empty lines
#                     categories.append(line)
#     except FileNotFoundError:
#         print("Warning: categories.txt not found. Using empty category list.")
#     except Exception as e:
#         print(f"Error loading categories: {e}")

#     return categories


def load_category_tags_from_file() -> Dict[str, List[str]]:
    """
    Load category tags from categories_tags.txt file.

    The file format is:
    Category Name:
    tag1
    tag2
    ...

    Returns:
        Dictionary mapping category names to lists of tags
    """
    category_tags = {}
    current_category = None

    try:
        with open(_get_data_file_path('categories_tags.txt'), 'r', encoding='utf-8') as f:
            for line in f:
                line = line.strip()

                if not line:  # Skip empty lines
                    continue

                if line.endswith(':'):  # This is a category header
                    current_category = line[:-1]  # Remove the colon
                    category_tags[current_category] = [current_category.lower()]
                elif current_category:  # This is a tag for the current category
                    category_tags[current_category].append(line.lower())
                    # category_tags[current_category].extend(line.split(' '))

    except FileNotFoundError:
        print("Warning: categories_tags.txt not found. Using empty tags.")
    except Exception as e:
        print(f"Error loading category tags: {e}")

    return category_tags


def create_categories_info() -> List[Dict[str, any]]:
    """
    Create the categories_info structure by combining data from both files.

    Returns:
        List of dictionaries with 'category' and 'tags' keys, compatible with existing classifier
    """
    # categories = load_categories_from_file()
    category_tags = load_category_tags_from_file()

    categories_info = []

    for category in category_tags:
        tags = category_tags[category]

        category_info = {
            'category': category,
            'tags': tags
        }

        categories_info.append(category_info)

    return categories_info


def get_all_categories() -> List[str]:
    """Get list of all available category names."""
    return [cat['category'] for cat in categories_info]


def get_category_tags(category_name: str) -> List[str]:
    """
    Get tags for a specific category.

    Args:
        category_name: Name of the category

    Returns:
        List of tags for the category, or empty list if category not found
    """
    for cat in categories_info:
        if cat['category'] == category_name:
            return cat['tags']
    return []


def search_categories_by_tag(tag: str, case_sensitive: bool = False) -> List[str]:
    """
    Find categories that contain a specific tag.

    Args:
        tag: Tag to search for
        case_sensitive: Whether to perform case-sensitive search

    Returns:
        List of category names that contain the tag
    """
    matching_categories = []
    search_tag = tag if case_sensitive else tag.lower()

    for cat in categories_info:
        category_tags = cat['tags'] if case_sensitive else [t.lower() for t in cat['tags']]
        if search_tag in category_tags:
            matching_categories.append(cat['category'])

    return matching_categories


def validate_category(category_name: str) -> bool:
    """
    Check if a category name is valid.

    Args:
        category_name: Name of the category to validate

    Returns:
        True if category exists, False otherwise
    """
    return category_name in get_all_categories()


def get_category_stats() -> Dict[str, int]:
    """
    Get statistics about the category system.

    Returns:
        Dictionary with statistics
    """
    total_categories = len(categories_info)
    total_tags = sum(len(cat['tags']) for cat in categories_info)
    avg_tags_per_category = total_tags / total_categories if total_categories > 0 else 0

    return {
        'total_categories': total_categories,
        'total_tags': total_tags,
        'average_tags_per_category': round(avg_tags_per_category, 2)
    }


# Load the categories data when the module is imported
categories_info = create_categories_info()


