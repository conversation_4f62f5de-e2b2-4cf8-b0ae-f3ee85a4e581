import sys, os

sys.path.append(os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__)))))

from domains.productcategory.classifier import classify_product, classify_product_v2, classify_product_dynamic

# from domains.productcategory.category import categories_info




# for cat in categories_info:
#     print(cat['category'])
#     print(cat['tags'])
#     print()


# sys.exit(0)


# Example usage
product_title = "Home Improvement eXODA Kabelschuh 25mm2 M8 4x für Batteriekabel"

print("=== CLASSIFICATION WITH MATCHING TAGS ===\n")

print("1. Standard Classification:")
result1 = classify_product(product_title, 6)
for i, res in enumerate(result1):
    print(f"   {i+1}. {res['category']} (Score: {res['score']:.3f})")
    if 'matching_tags' in res and res['matching_tags']:
        print(f"      Matching Tags ({res['total_matching_tags']}):")
        for tag_info in res['matching_tags'][:5]:  # Show top 5 matching tags
            print(f"        - {tag_info['tag']} ({tag_info['match_type']})")
        if len(res['matching_tags']) > 5:
            print(f"        ... and {len(res['matching_tags']) - 5} more")
    else:
        print("      No direct tag matches found")
    print()

# print("2. Classification with Threshold (0.25):")
# result2 = classify_product_v2(product_title, threshold=0.25)
# for i, res in enumerate(result2):
#     print(f"   {i+1}. {res['category']} (Score: {res['score']:.3f})")
#     if 'matching_tags' in res and res['matching_tags']:
#         print(f"      Matching Tags ({res['total_matching_tags']}):")
#         for tag_info in res['matching_tags'][:3]:  # Show top 3 matching tags
#             print(f"        - {tag_info['tag']} ({tag_info['match_type']})")
#     print()

# print("3. Dynamic Classification:")
# result3 = classify_product_dynamic(product_title)
# for i, res in enumerate(result3):
#     print(f"   {i+1}. {res['category']} (Score: {res['score']:.3f})")
#     if 'matching_tags' in res and res['matching_tags']:
#         print(f"      Top Matching Tags:")
#         for tag_info in res['matching_tags'][:3]:
#             print(f"        - {tag_info['tag']} ({tag_info['match_type']})")
#     print()

# # Test with a different product
# print("=" * 60)
# product_title2 = "Apple iPhone 15 Pro Max 256GB Blue Titanium"
# print(f"Testing with: {product_title2}")
# print()

# result4 = classify_product(product_title2, top_k=2)
# for i, res in enumerate(result4):
#     print(f"{i+1}. {res['category']} (Score: {res['score']:.3f})")
#     if 'matching_tags' in res and res['matching_tags']:
#         print(f"   Matching Tags ({res['total_matching_tags']}):")
#         for tag_info in res['matching_tags'][:3]:
#             print(f"     - {tag_info['tag']} ({tag_info['match_type']})")
#     print()

