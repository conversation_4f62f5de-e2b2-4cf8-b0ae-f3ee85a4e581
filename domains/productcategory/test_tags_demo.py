#!/usr/bin/env python3
"""
Demo script to show the matching tags feature in action.
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__)))))

from domains.productcategory.classifier import classify_product, classify_product_v2, classify_product_dynamic

def test_product_with_tags(title, description=""):
    """Test a product and show matching tags."""
    print(f"🔍 Testing: {title}")
    if description:
        print(f"   {description}")
    print("-" * 80)
    
    results = classify_product(title, top_k=3)
    
    for i, res in enumerate(results):
        print(f"{i+1}. {res['category']} (Score: {res['score']:.3f})")
        
        if 'matching_tags' in res and res['matching_tags']:
            print(f"   ✅ Found {res['total_matching_tags']} matching tags:")
            for tag_info in res['matching_tags'][:5]:  # Show top 5
                match_type = "🎯" if tag_info['match_type'] == 'exact' else "🔗"
                print(f"      {match_type} {tag_info['tag']} ({tag_info['match_type']})")
            if len(res['matching_tags']) > 5:
                print(f"      ... and {len(res['matching_tags']) - 5} more")
        else:
            print("   ❌ No direct tag matches found")
        print()
    
    print("=" * 80)
    print()

def main():
    print("🏷️  PRODUCT CLASSIFICATION WITH MATCHING TAGS DEMO")
    print("=" * 80)
    print()
    
    # Test various products that should have good tag matches
    test_products = [
        ("Samsung 65-inch 4K Smart TV QLED", "Should match Consumer Electronics"),
        ("Nike Air Max Running Shoes Size 10", "Should match Footwear"),
        ("Instant Pot 8-Quart Electric Pressure Cooker", "Should match Compact Appliances"),
        ("DEWALT 20V MAX Cordless Drill Driver Kit", "Should match Tools and Home Improvement"),
        ("Baby Stroller with Car Seat Travel System", "Should match Baby Products"),
        ("Automotive Car Battery 12V AGM", "Should match Automotive and Powersports"),
        ("Wireless Bluetooth Headphones with Noise Cancelling", "Should match Electronic Accessories"),
        ("Memory Foam Queen Size Mattress", "Should match Mattresses"),
        ("Garden Hose 50ft with Spray Nozzle", "Should match Lawn and Garden"),
        ("Office Desk Chair Ergonomic with Lumbar Support", "Should match Office Products")
    ]
    
    for title, description in test_products:
        test_product_with_tags(title, description)

if __name__ == "__main__":
    main()
