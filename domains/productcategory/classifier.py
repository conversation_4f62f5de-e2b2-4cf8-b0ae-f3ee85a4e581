import numpy as np
from sklearn.metrics.pairwise import cosine_similarity
from sentence_transformers import SentenceTransformer
from domains.productcategory.category import create_categories_info
from typing import List, Dict

categories_info = create_categories_info()

# model = SentenceTransformer('all-MiniLM-L6-v2') #commented out to avoid reloading model everytime

# # Precompute embeddings for categories by combining tags as single text
# for cat in categories_info:
#     combined_tags = " ".join(cat['tags'])
#     cat['embedding'] = model.encode(combined_tags)


def find_matching_tags(title: str, category_tags: List[str], min_match_length: int = 3) -> List[Dict[str, any]]:
    """
    Find tags that match with the product title.

    Args:
        title: Product title to match against
        category_tags: List of tags for a category
        min_match_length: Minimum length of matching substring

    Returns:
        List of dictionaries with tag and match information
    """
    title_lower = title.lower()
    matching_tags = []

    for tag in category_tags:
        tag_lower = tag.lower()

        # Check for exact tag match
        if tag_lower in title_lower:
            matching_tags.append({
                'tag': tag,
                'match_type': 'exact',
                'match_strength': len(tag_lower)
            })
            continue

        # Check for partial word matches
        tag_words = tag_lower.split()
        title_words = title_lower.split()

        matched_words = []
        for tag_word in tag_words:
            if len(tag_word) >= min_match_length:
                for title_word in title_words:
                    if tag_word in title_word or title_word in tag_word:
                        matched_words.append(tag_word)
                        break

        # If most words in tag are matched, consider it a partial match
        if len(matched_words) >= len(tag_words) * 0.6:  # 60% of words matched
            matching_tags.append({
                'tag': tag,
                'match_type': 'partial',
                'match_strength': len(matched_words),
                'matched_words': matched_words
            })

    # Sort by match strength (stronger matches first)
    matching_tags.sort(key=lambda x: x['match_strength'], reverse=True)

    return matching_tags


def create_model():
    model = SentenceTransformer('all-MiniLM-L6-v2')
    # Precompute embeddings for categories by combining tags as single text
    for cat in categories_info:
        combined_tags = " ".join(cat['tags'])
        cat['embedding'] = model.encode(combined_tags)
        cat['combined_tags'] = combined_tags
    return model
#def

def classify_product(title, top_k=3, include_tags=True):
    """Return top K matching categories for a product title."""

    model = create_model()
    title_embedding = model.encode(title)
    embeddings = np.array([c['embedding'] for c in categories_info])
    sims = cosine_similarity([title_embedding], embeddings)[0]
    top_indices = sims.argsort()[-top_k:][::-1]
    results = []
    for idx in top_indices:
        result = {
            'category': categories_info[idx]['category'],
            'score': sims[idx]
        }

        # Add matching tags information if requested
        if include_tags:
            matching_tags = find_matching_tags(title, categories_info[idx]['tags'])
            result['matching_tags'] = matching_tags
            result['total_matching_tags'] = len(matching_tags)

        results.append(result)
    return results
#def



def classify_product_v2(title, top_k=3, threshold=0.5, include_tags=True):
    """
    Classify product title into categories.
    Returns categories with similarity score >= threshold.
    """
    model = create_model()
    title_embedding = model.encode(title)
    embeddings = np.array([c['embedding'] for c in categories_info])
    sims = cosine_similarity([title_embedding], embeddings)[0]

    # Filter by threshold
    results = []
    for idx, score in enumerate(sims):
        if score >= threshold:
            result = {
                'category': categories_info[idx]['category'],
                'score': score
            }

            # Add matching tags information if requested
            if include_tags:
                matching_tags = find_matching_tags(title, categories_info[idx]['tags'])
                result['matching_tags'] = matching_tags
                result['total_matching_tags'] = len(matching_tags)

            results.append(result)

    # Sort results descending by score
    results.sort(key=lambda x: x['score'], reverse=True)

    # Return top_k results
    return results[:top_k]
#def


def classify_product_dynamic(title, margin=0.1, include_tags=True):
    """
    Classify product title into categories dynamically.
    Returns all categories with score within margin of top score.
    """
    model = create_model()
    title_embedding = model.encode(title)
    embeddings = np.array([c['embedding'] for c in categories_info])
    sims = cosine_similarity([title_embedding], embeddings)[0]

    # Find top category
    top_idx = np.argmax(sims)
    top_score = sims[top_idx]

    # Create result for top category
    top_result = {
        'category': categories_info[top_idx]['category'],
        'score': top_score
    }

    if include_tags:
        matching_tags = find_matching_tags(title, categories_info[top_idx]['tags'])
        top_result['matching_tags'] = matching_tags
        top_result['total_matching_tags'] = len(matching_tags)

    results = [top_result]

    # Add categories within margin of top_score
    for idx, score in enumerate(sims):
        if idx != top_idx and score >= top_score - margin:
            result = {
                'category': categories_info[idx]['category'],
                'score': score
            }

            if include_tags:
                matching_tags = find_matching_tags(title, categories_info[idx]['tags'])
                result['matching_tags'] = matching_tags
                result['total_matching_tags'] = len(matching_tags)

            results.append(result)

    # Sort descending
    results.sort(key=lambda x: x['score'], reverse=True)
    return results
#def
