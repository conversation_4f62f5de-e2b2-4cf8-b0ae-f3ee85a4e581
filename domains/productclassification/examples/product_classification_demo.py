#!/usr/bin/env python3
"""
Product Classification Demo

This script demonstrates how to use the product classification system
for training and making predictions.
"""

import sys
import os

# Add the parent directory to the path
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from domains.productclassification.classifier_trainer import ProductClassificationTrainerManager, main_train
from domains.productclassification.data_loader import ProductDataLoader
from domains.productclassification.product_classifier_model import ClassificationConfig


def demo_training():
    """Demonstrate model training"""
    print("="*60)
    print("PRODUCT CLASSIFICATION TRAINING DEMO")
    print("="*60)
    
    print("\n1. Training a new model with synthetic data...")
    
    # Train with synthetic data (no database required)
    result = main_train(
        force_new_model=True,
        use_real_data=False,  # Set to True if you have database access
        use_synthetic_data=True,
        real_data_limit=1000,
        synthetic_data_limit=2000  # Small dataset for demo
    )
    
    if result['success']:
        print(f"✅ Training completed successfully!")
        print(f"   Model: {result['model_name']}")
        print(f"   Training samples: {result['training_data_size']}")
        print(f"   Validation samples: {result['validation_data_size']}")
        print(f"   Final accuracy: {result['final_accuracy']:.4f}")
        print(f"   Training time: {result['training_time']:.1f} seconds")
        return True
    else:
        print(f"❌ Training failed: {result['error']}")
        return False


def demo_predictions():
    """Demonstrate making predictions"""
    print("\n" + "="*60)
    print("PRODUCT CLASSIFICATION PREDICTION DEMO")
    print("="*60)
    
    try:
        # Initialize trainer manager
        trainer_manager = ProductClassificationTrainerManager()
        
        # Sample product titles for testing
        test_products = [
            "Apple iPhone 13 Pro Max 128GB Blue Unlocked",
            "Nike Air Max 270 Running Shoes Men's Size 10 Black",
            "Samsung 55-inch 4K Smart TV with HDR",
            "Baby Stroller Travel System with Car Seat",
            "Professional Chef Knife Set 8-Piece Stainless Steel",
            "Sony WH-1000XM4 Wireless Noise Cancelling Headphones",
            "Men's Cotton T-Shirt Blue Large Crew Neck",
            "Laptop Messenger Bag 15.6 inch Water Resistant",
            "Car Battery 12V Heavy Duty Automotive",
            "Garden Hose 50ft Heavy Duty Expandable",
            "Office Chair Ergonomic Black Leather Executive",
            "Whey Protein Powder Vanilla 5lb Container",
            "Dog Food Dry Adult Large Breed Chicken",
            "Cordless Drill 18V with Battery and Charger",
            "Board Game Strategy Family Fun Ages 8+",
            "Sunglasses Polarized UV Protection Aviator Style",
            "Camping Tent 4 Person Waterproof Easy Setup",
            "Coffee Maker Single Serve K-Cup Compatible",
            "Smart Watch Fitness Tracker Heart Rate Monitor",
            "Memory Foam Mattress Queen Size Medium Firm"
        ]
        
        print(f"\n2. Making predictions for {len(test_products)} products...")
        
        # Make predictions
        predictions = trainer_manager.predict_products(test_products)
        
        print("\n" + "-"*80)
        print("PREDICTION RESULTS")
        print("-"*80)
        
        # Display results
        correct_predictions = 0
        total_predictions = len(predictions)
        
        for i, pred in enumerate(predictions, 1):
            title = pred['title']
            category = pred['predicted_category']
            confidence = pred['confidence']
            method = pred['method']
            rule_based = pred['rule_based_category']
            
            print(f"\n{i:2d}. {title[:60]}")
            print(f"    Category: {category}")
            print(f"    Confidence: {confidence:.3f}")
            print(f"    Method: {method}")
            
            if rule_based and rule_based != category:
                print(f"    Rule-based: {rule_based}")
            
            # Show top 3 predictions
            print("    Top predictions:")
            for j, top_pred in enumerate(pred['top_predictions'][:3], 1):
                marker = "→" if j == 1 else " "
                print(f"    {marker} {j}. {top_pred['category']} ({top_pred['confidence']:.3f})")
        
        print("\n" + "-"*80)
        print(f"Processed {total_predictions} products successfully")
        
        # Show method distribution
        method_counts = {}
        for pred in predictions:
            method = pred['method']
            method_counts[method] = method_counts.get(method, 0) + 1
        
        print("\nMethod distribution:")
        for method, count in method_counts.items():
            percentage = (count / total_predictions) * 100
            print(f"  {method}: {count} ({percentage:.1f}%)")
        
        return True
        
    except Exception as e:
        print(f"❌ Prediction demo failed: {str(e)}")
        return False


def demo_rule_based_classification():
    """Demonstrate rule-based classification"""
    print("\n" + "="*60)
    print("RULE-BASED CLASSIFICATION DEMO")
    print("="*60)
    
    try:
        # Initialize data loader to access preprocessor
        config = ClassificationConfig()
        data_loader = ProductDataLoader(config)
        
        # Test products with obvious category matches
        test_cases = [
            ("Apple iPhone 13 Pro Max", "Consumer Electronics"),
            ("Samsung Galaxy smartphone", "Consumer Electronics"),
            ("Nike running shoes", "Footwear"),
            ("Cotton t-shirt", "Clothing and Accessories"),
            ("Baby stroller", "Baby Products"),
            ("Kitchen knife set", "Home and Kitchen"),
            ("Bluetooth headphones", "Consumer Electronics"),
            ("Car battery", "Automotive and Powersports"),
            ("Garden hose", "Lawn and Garden"),
            ("Office chair", "Furniture"),
            ("Protein powder", "Beauty, Health and Personal Care"),
            ("Dog food", "Pet Products"),
            ("Power drill", "Tools and Home Improvement"),
            ("Board game", "Toys and Games"),
            ("Camping tent", "Sports and Outdoors")
        ]
        
        print("\n3. Testing rule-based classification...")
        print("\n" + "-"*70)
        
        correct = 0
        total = len(test_cases)
        
        for title, expected in test_cases:
            predicted = data_loader.preprocessor.predict_category_from_tags(title)
            
            if predicted:
                status = "✓" if predicted == expected else "✗"
                if predicted == expected:
                    correct += 1
            else:
                status = "?"
                predicted = "No match"
            
            print(f"{status} {title:<30} → {predicted}")
            if predicted != expected and predicted != "No match":
                print(f"  Expected: {expected}")
        
        print("-"*70)
        accuracy = (correct / total) * 100
        print(f"Rule-based accuracy: {correct}/{total} ({accuracy:.1f}%)")
        
        return True
        
    except Exception as e:
        print(f"❌ Rule-based demo failed: {str(e)}")
        return False


def demo_synthetic_data_generation():
    """Demonstrate synthetic data generation"""
    print("\n" + "="*60)
    print("SYNTHETIC DATA GENERATION DEMO")
    print("="*60)
    
    try:
        # Initialize data loader
        data_loader = ProductDataLoader()
        
        print("\n4. Generating synthetic training data...")
        
        # Generate a small sample of synthetic data
        synthetic_data = data_loader.generate_synthetic_training_data(50)
        
        print(f"Generated {len(synthetic_data)} synthetic products")
        
        # Show samples by category
        category_samples = {}
        for item in synthetic_data:
            category = item['category']
            if category not in category_samples:
                category_samples[category] = []
            if len(category_samples[category]) < 3:  # Max 3 samples per category
                category_samples[category].append(item['title'])
        
        print("\nSample synthetic products by category:")
        print("-"*50)
        
        for category, titles in sorted(category_samples.items()):
            print(f"\n{category}:")
            for title in titles:
                print(f"  • {title}")
        
        return True
        
    except Exception as e:
        print(f"❌ Synthetic data demo failed: {str(e)}")
        return False


def main():
    """Run the complete demo"""
    print("🚀 Starting Product Classification System Demo")
    print("This demo will show you how to train and use the product classification system.")
    
    success_count = 0
    total_demos = 4
    
    # Run all demos
    if demo_synthetic_data_generation():
        success_count += 1
    
    if demo_rule_based_classification():
        success_count += 1
    
    if demo_training():
        success_count += 1
        
        # Only run predictions if training succeeded
        if demo_predictions():
            success_count += 1
    else:
        print("\n⚠️  Skipping prediction demo due to training failure")
    
    # Summary
    print("\n" + "="*60)
    print("DEMO SUMMARY")
    print("="*60)
    print(f"Completed {success_count}/{total_demos} demos successfully")
    
    if success_count == total_demos:
        print("🎉 All demos completed successfully!")
        print("\nNext steps:")
        print("1. Try the CLI commands: python3 -m cli.ml product-classification --help")
        print("2. Start the API server: uvicorn api.app:app --host 0.0.0.0 --port 8000")
        print("3. Test the API endpoints at http://localhost:8000/docs")
    else:
        print("⚠️  Some demos failed. Check the error messages above.")
    
    print("\n📚 For more information, see docs/PRODUCT_CLASSIFICATION.md")


if __name__ == "__main__":
    main()
