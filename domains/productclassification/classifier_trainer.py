#!/usr/bin/env python3

import os
import json
from datetime import datetime, timezone
from typing import List, Dict, Any, Optional
import pandas as pd

from domains.productclassification.product_classifier_model import (
    ProductClassifierTrainer, 
    ClassificationConfig
)
from domains.productclassification.data_loader import ProductDataLoader
from domains.repository.eprice_repository import ElasticPriceRepository
from domains.models.trained_model import TrainedModel
from facades.redis.redisdb import RedisFacade
from lib.json import json_encode, json_decode


class ProductClassificationTrainerManager:
    """Manager for product classification model training following existing patterns"""
    
    def __init__(self, model_name: str = "product_classifier_v1"):
        self.model_name = model_name
        self.config = ClassificationConfig()
        self.trainer = ProductClassifierTrainer(self.config)
        self.data_loader = ProductDataLoader(self.config)
        self.repo = ElasticPriceRepository.new_instance()
        self.redis_cache = RedisFacade.instance()
        
        # Training metrics
        self.training_start_time = None
        self.training_end_time = None
        self.training_data_size = 0
        self.validation_data_size = 0
        self.final_loss = 0.0
        self.final_accuracy = 0.0
        
    def prepare_training_data(self, 
                            use_real_data: bool = True,
                            use_synthetic_data: bool = True,
                            real_data_limit: int = 5000,
                            synthetic_data_limit: int = 10000,
                            customer_ids: List[int] = None) -> tuple[List[str], List[str]]:
        """Prepare training data"""
        print("Preparing training data...")
        
        titles, categories = self.data_loader.create_training_dataset(
            use_real_data=use_real_data,
            use_synthetic_data=use_synthetic_data,
            real_data_limit=real_data_limit,
            synthetic_data_limit=synthetic_data_limit,
            customer_ids=customer_ids
        )
        
        self.training_data_size = int(len(titles) * 0.8)  # 80% for training
        self.validation_data_size = len(titles) - self.training_data_size
        
        print(f"Training data prepared: {len(titles)} total samples")
        print(f"Training set: {self.training_data_size} samples")
        print(f"Validation set: {self.validation_data_size} samples")
        
        return titles, categories
    
    def train_model(self, 
                   force_new_model: bool = False,
                   use_real_data: bool = True,
                   use_synthetic_data: bool = True,
                   real_data_limit: int = 5000,
                   synthetic_data_limit: int = 10000,
                   customer_ids: List[int] = None,
                   save_to_db: bool = True) -> Dict[str, Any]:
        """Train the product classification model"""
        
        print(f"Starting training for model: {self.model_name}")
        self.training_start_time = datetime.now(timezone.utc)
        
        try:
            # Check if we should load existing model
            if not force_new_model:
                try:
                    existing_model = self.load_existing_model()
                    if existing_model:
                        print("Loaded existing model for continued training")
                except Exception as e:
                    print(f"Could not load existing model: {e}")
                    print("Starting with new model")
            
            # Prepare training data
            titles, categories = self.prepare_training_data(
                use_real_data=use_real_data,
                use_synthetic_data=use_synthetic_data,
                real_data_limit=real_data_limit,
                synthetic_data_limit=synthetic_data_limit,
                customer_ids=customer_ids
            )
            
            if len(titles) == 0:
                raise ValueError("No training data available")
            
            # Train the model
            print("Starting model training...")
            training_history = self.trainer.train(titles, categories)
            
            self.training_end_time = datetime.now(timezone.utc)
            
            # Get final metrics
            if training_history['val_loss']:
                self.final_loss = training_history['val_loss'][-1]
                self.final_accuracy = training_history['val_accuracy'][-1]
            
            print(f"Training completed!")
            print(f"Final validation loss: {self.final_loss:.4f}")
            print(f"Final validation accuracy: {self.final_accuracy:.4f}")
            
            # Save model
            if save_to_db:
                self.save_trained_model(training_history)
            
            # Test the model
            self.test_model()
            
            return {
                'success': True,
                'model_name': self.model_name,
                'training_data_size': self.training_data_size,
                'validation_data_size': self.validation_data_size,
                'final_loss': self.final_loss,
                'final_accuracy': self.final_accuracy,
                'training_time': (self.training_end_time - self.training_start_time).total_seconds(),
                'training_history': training_history
            }
            
        except Exception as e:
            print(f"Training failed: {str(e)}")
            return {
                'success': False,
                'error': str(e),
                'model_name': self.model_name
            }
    
    def load_existing_model(self) -> Optional[TrainedModel]:
        """Load existing trained model from database"""
        try:
            trained_model = self.repo.get_last_active_trained_model(self.model_name)
            if trained_model and trained_model.model_dump:
                # For classification model, we load from files instead of model_dump
                # This is because we have separate model and vocabulary files
                if os.path.exists(self.config.model_path) and os.path.exists(self.config.vocab_path):
                    self.trainer.load_model()
                    return trained_model
            return None
        except Exception as e:
            print(f"Error loading existing model: {e}")
            return None
    
    def save_trained_model(self, training_history: Dict[str, List[float]]):
        """Save trained model to database following existing pattern"""
        try:
            # Create model info
            model_info = {
                'model_name': self.model_name,
                'model_type': 'product_classification',
                'config': {
                    'vocab_size': self.config.vocab_size,
                    'embed_dim': self.config.embed_dim,
                    'hidden_dim': self.config.hidden_dim,
                    'num_classes': self.config.num_classes,
                    'max_sequence_length': self.config.max_sequence_length,
                    'learning_rate': self.config.learning_rate,
                    'batch_size': self.config.batch_size,
                    'num_epochs': self.config.num_epochs,
                    'dropout_rate': self.config.dropout_rate
                },
                'categories': self.trainer.preprocessor.categories,
                'vocab_size_actual': self.trainer.preprocessor.vocab_size,
                'model_path': self.config.model_path,
                'vocab_path': self.config.vocab_path,
                'training_history': training_history
            }
            
            # Create TrainedModel instance
            trained_model = TrainedModel(
                id=None,
                model_name=self.model_name,
                active=True,
                model_info=model_info,
                model_dump=None,  # We save to files instead
                loss=self.final_loss,
                accuracy=self.final_accuracy,
                training_data_size=self.training_data_size,
                validation_data_size=self.validation_data_size,
                start_time=self.training_start_time,
                end_time=self.training_end_time,
                created_at=datetime.now(timezone.utc)
            )
            
            # Save to database
            self.repo.store_trained_model(
                model_name=trained_model.model_name,
                model_dump=None,  # We save to files instead
                model_info=trained_model.model_info,
                start_time=trained_model.start_time,
                end_time=trained_model.end_time,
                loss=trained_model.loss,
                accuracy=trained_model.accuracy,
                training_data_size=trained_model.training_data_size,
                validation_data_size=trained_model.validation_data_size
            )
            
            # Cache model info in Redis
            cache_key = f"product_classifier_last_trained_model_{self.model_name}"
            self.redis_cache.db_query(1).set(
                cache_key, 
                json_encode(model_info), 
                ex=24*60*60  # 24 hours
            )
            
            print(f"Model saved to database and cached")
            
        except Exception as e:
            print(f"Error saving trained model: {e}")
    
    def test_model(self):
        """Test the trained model with sample data"""
        print("\nTesting trained model...")
        
        # Get test samples
        test_samples = self.data_loader.create_test_samples()
        
        correct_predictions = 0
        total_predictions = len(test_samples)
        
        print("\nTest Results:")
        print("-" * 80)
        
        for sample in test_samples:
            try:
                result = self.trainer.predict_single(sample['title'])
                
                predicted_category = result['predicted_category']
                expected_category = sample['expected_category']
                confidence = result['confidence']
                method = result['method']
                
                is_correct = predicted_category == expected_category
                if is_correct:
                    correct_predictions += 1
                
                status = "✓" if is_correct else "✗"
                print(f"{status} {sample['title'][:50]:<50}")
                print(f"   Expected: {expected_category}")
                print(f"   Predicted: {predicted_category} ({confidence:.3f}) [{method}]")
                
                if not is_correct and result['rule_based_category']:
                    print(f"   Rule-based: {result['rule_based_category']}")
                
                print()
                
            except Exception as e:
                print(f"✗ Error predicting for: {sample['title']}")
                print(f"   Error: {e}")
                print()
        
        accuracy = correct_predictions / total_predictions
        print(f"Test Accuracy: {accuracy:.3f} ({correct_predictions}/{total_predictions})")
        print("-" * 80)
    
    def predict_products(self, titles: List[str]) -> List[Dict[str, Any]]:
        """Predict categories for a list of product titles"""
        if self.trainer.model is None:
            # Try to load the model
            try:
                self.trainer.load_model()
            except Exception as e:
                raise ValueError(f"No trained model available. Train a model first. Error: {e}")
        
        return self.trainer.predict_batch(titles)
    
    def get_model_info(self) -> Dict[str, Any]:
        """Get information about the current model"""
        try:
            cache_key = f"product_classifier_last_trained_model_{self.model_name}"
            cached_info = self.redis_cache.db_query(1).get(cache_key)
            
            if cached_info:
                return json_decode(cached_info)
            
            # Try to load from database
            trained_model = self.repo.get_last_active_trained_model(self.model_name)
            if trained_model and trained_model.model_info:
                return trained_model.model_info
            
            return {"error": "No model information available"}
            
        except Exception as e:
            return {"error": f"Error getting model info: {e}"}


def main_train(force_new_model: bool = False, 
               use_real_data: bool = True,
               use_synthetic_data: bool = True,
               real_data_limit: int = 5000,
               synthetic_data_limit: int = 10000):
    """Main training function"""
    
    trainer_manager = ProductClassificationTrainerManager()
    
    result = trainer_manager.train_model(
        force_new_model=force_new_model,
        use_real_data=use_real_data,
        use_synthetic_data=use_synthetic_data,
        real_data_limit=real_data_limit,
        synthetic_data_limit=synthetic_data_limit
    )
    
    if result['success']:
        print(f"\n🎉 Training completed successfully!")
        print(f"Model: {result['model_name']}")
        print(f"Training data: {result['training_data_size']} samples")
        print(f"Validation data: {result['validation_data_size']} samples")
        print(f"Final accuracy: {result['final_accuracy']:.4f}")
        print(f"Training time: {result['training_time']:.1f} seconds")
    else:
        print(f"\n❌ Training failed: {result['error']}")
    
    return result


if __name__ == "__main__":
    # Test training with synthetic data only
    print("Starting product classification training...")
    
    result = main_train(
        force_new_model=True,
        use_real_data=False,  # Set to True if you have real product data
        use_synthetic_data=True,
        synthetic_data_limit=2000  # Small dataset for testing
    )
    
    if result['success']:
        print("\nTesting predictions...")
        trainer_manager = ProductClassificationTrainerManager()
        
        test_titles = [
            "Apple iPhone 13 Pro Max 128GB Blue",
            "Nike Air Max 270 Running Shoes",
            "Samsung 55-inch 4K Smart TV",
            "Baby Stroller with Car Seat",
            "Kitchen Knife Set Professional"
        ]
        
        predictions = trainer_manager.predict_products(test_titles)
        
        print("\nPrediction Results:")
        for pred in predictions:
            print(f"Title: {pred['title']}")
            print(f"Category: {pred['predicted_category']} ({pred['confidence']:.3f})")
            print(f"Method: {pred['method']}")
            print()
