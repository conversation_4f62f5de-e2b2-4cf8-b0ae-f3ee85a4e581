#!/usr/bin/env python3

import unittest
import tempfile
import os
from unittest.mock import patch, MagicMock
import torch

from domains.productclassification.product_classifier_model import (
    TextPreprocessor, 
    ProductClassifierNN, 
    ProductClassifierTrainer,
    ClassificationConfig
)
from domains.productclassification.data_loader import ProductDataLoader
from domains.productclassification.classifier_trainer import ProductClassificationTrainerManager


class TestTextPreprocessor(unittest.TestCase):
    """Test cases for TextPreprocessor"""
    
    def setUp(self):
        self.config = ClassificationConfig()
        self.preprocessor = TextPreprocessor(self.config)
    
    def test_clean_text(self):
        """Test text cleaning functionality"""
        # Test basic cleaning
        text = "Apple iPhone 13 Pro Max - 128GB (Blue)!"
        cleaned = self.preprocessor.clean_text(text)
        expected = "apple iphone 13 pro max 128gb blue"
        self.assertEqual(cleaned, expected)
        
        # Test empty text
        self.assertEqual(self.preprocessor.clean_text(""), "")
        self.assertEqual(self.preprocessor.clean_text(None), "")
        
        # Test special characters
        text = "Product@#$%^&*()_+{}|:<>?[]\\;'\",./"
        cleaned = self.preprocessor.clean_text(text)
        self.assertEqual(cleaned, "product")
    
    def test_tokenize(self):
        """Test tokenization"""
        text = "Apple iPhone 13 Pro Max"
        tokens = self.preprocessor.tokenize(text)
        expected = ["apple", "iphone", "13", "pro", "max"]
        self.assertEqual(tokens, expected)
        
        # Test empty text
        self.assertEqual(self.preprocessor.tokenize(""), [])
    
    def test_build_vocabulary(self):
        """Test vocabulary building"""
        texts = [
            "Apple iPhone 13 Pro Max",
            "Samsung Galaxy S21 Ultra",
            "iPhone 12 Pro",
            "Galaxy Note 20"
        ]
        
        self.preprocessor.build_vocabulary(texts)
        
        # Check that vocabulary was built
        self.assertGreater(self.preprocessor.vocab_size, 2)  # More than <PAD> and <UNK>
        
        # Check that common words are in vocabulary
        self.assertIn("iphone", self.preprocessor.word_to_idx)
        self.assertIn("pro", self.preprocessor.word_to_idx)
        self.assertIn("galaxy", self.preprocessor.word_to_idx)
    
    def test_text_to_sequence(self):
        """Test text to sequence conversion"""
        # Build vocabulary first
        texts = ["Apple iPhone 13 Pro Max", "Samsung Galaxy S21"]
        self.preprocessor.build_vocabulary(texts)
        
        # Test conversion
        text = "iPhone Pro"
        sequence = self.preprocessor.text_to_sequence(text)
        
        # Check sequence length
        self.assertEqual(len(sequence), self.config.max_sequence_length)
        
        # Check that known words are not <UNK>
        iphone_idx = self.preprocessor.word_to_idx.get("iphone", 1)
        pro_idx = self.preprocessor.word_to_idx.get("pro", 1)
        
        self.assertEqual(sequence[0], iphone_idx)
        self.assertEqual(sequence[1], pro_idx)
        
        # Check padding
        for i in range(2, self.config.max_sequence_length):
            self.assertEqual(sequence[i], 0)  # Should be padded with 0


class TestProductClassifierNN(unittest.TestCase):
    """Test cases for ProductClassifierNN"""
    
    def setUp(self):
        self.config = ClassificationConfig()
        self.config.num_classes = 5  # Small number for testing
        self.vocab_size = 1000
        self.model = ProductClassifierNN(self.config, self.vocab_size)
    
    def test_model_initialization(self):
        """Test model initialization"""
        self.assertIsInstance(self.model.embedding, torch.nn.Embedding)
        self.assertIsInstance(self.model.lstm, torch.nn.LSTM)
        self.assertIsInstance(self.model.attention, torch.nn.MultiheadAttention)
        self.assertIsInstance(self.model.classifier, torch.nn.Sequential)
    
    def test_forward_pass(self):
        """Test forward pass"""
        batch_size = 2
        seq_len = self.config.max_sequence_length
        
        # Create dummy input
        input_ids = torch.randint(0, self.vocab_size, (batch_size, seq_len))
        attention_mask = torch.ones(batch_size, seq_len)
        
        # Forward pass
        logits = self.model(input_ids, attention_mask)
        
        # Check output shape
        expected_shape = (batch_size, self.config.num_classes)
        self.assertEqual(logits.shape, expected_shape)
    
    def test_predict(self):
        """Test prediction method"""
        batch_size = 2
        seq_len = self.config.max_sequence_length
        
        # Create dummy input
        input_ids = torch.randint(0, self.vocab_size, (batch_size, seq_len))
        attention_mask = torch.ones(batch_size, seq_len)
        
        # Make prediction
        probabilities = self.model.predict(input_ids, attention_mask)
        
        # Check output shape and properties
        expected_shape = (batch_size, self.config.num_classes)
        self.assertEqual(probabilities.shape, expected_shape)
        
        # Check that probabilities sum to 1
        prob_sums = torch.sum(probabilities, dim=1)
        torch.testing.assert_close(prob_sums, torch.ones(batch_size), atol=1e-6, rtol=1e-6)


class TestProductDataLoader(unittest.TestCase):
    """Test cases for ProductDataLoader"""
    
    def setUp(self):
        self.config = ClassificationConfig()
        # Create temporary category files for testing
        self.temp_dir = tempfile.mkdtemp()
        self.config.categories_path = os.path.join(self.temp_dir, "categories.txt")
        self.config.tags_path = os.path.join(self.temp_dir, "categories_tags.txt")
        
        # Create test category files
        with open(self.config.categories_path, 'w') as f:
            f.write("Electronics\nClothing\nBooks\n")
        
        with open(self.config.tags_path, 'w') as f:
            f.write("Electronics:\niphone\nsamsung\ntv\n\nClothing:\nshirt\npants\nshoes\n\nBooks:\nnovel\ntextbook\n")
        
        self.data_loader = ProductDataLoader(self.config)
    
    def tearDown(self):
        # Clean up temporary files
        import shutil
        shutil.rmtree(self.temp_dir)
    
    def test_generate_synthetic_data(self):
        """Test synthetic data generation"""
        synthetic_data = self.data_loader.generate_synthetic_training_data(100)
        
        # Check that data was generated
        self.assertGreater(len(synthetic_data), 0)
        self.assertLessEqual(len(synthetic_data), 100)
        
        # Check data structure
        for item in synthetic_data[:5]:
            self.assertIn('title', item)
            self.assertIn('category', item)
            self.assertIn('asin', item)
            self.assertIn('synthetic', item)
            self.assertTrue(item['synthetic'])
    
    @patch('domains.productclassification.data_loader.ProductRepository')
    def test_create_training_dataset(self, mock_repo):
        """Test training dataset creation"""
        # Mock repository to avoid database dependency
        mock_repo.new_instance.return_value.get_customer_ids.return_value = []
        
        # Test with synthetic data only
        titles, categories = self.data_loader.create_training_dataset(
            use_real_data=False,
            use_synthetic_data=True,
            synthetic_data_limit=50
        )
        
        # Check that data was created
        self.assertGreater(len(titles), 0)
        self.assertEqual(len(titles), len(categories))
        
        # Check that all categories are valid
        valid_categories = set(self.data_loader.preprocessor.categories)
        for category in categories:
            self.assertIn(category, valid_categories)


class TestProductClassificationTrainerManager(unittest.TestCase):
    """Test cases for ProductClassificationTrainerManager"""
    
    def setUp(self):
        self.temp_dir = tempfile.mkdtemp()
        
        # Create test category files
        categories_path = os.path.join(self.temp_dir, "categories.txt")
        tags_path = os.path.join(self.temp_dir, "categories_tags.txt")
        
        with open(categories_path, 'w') as f:
            f.write("Electronics\nClothing\nBooks\n")
        
        with open(tags_path, 'w') as f:
            f.write("Electronics:\niphone\nsamsung\n\nClothing:\nshirt\npants\n\nBooks:\nnovel\n")
        
        # Create trainer manager with custom config
        self.trainer_manager = ProductClassificationTrainerManager()
        self.trainer_manager.config.categories_path = categories_path
        self.trainer_manager.config.tags_path = tags_path
        self.trainer_manager.config.num_epochs = 2  # Quick training for tests
        self.trainer_manager.config.batch_size = 4
    
    def tearDown(self):
        import shutil
        shutil.rmtree(self.temp_dir)
    
    @patch('domains.productclassification.classifier_trainer.ElasticPriceRepository')
    def test_prepare_training_data(self, mock_repo):
        """Test training data preparation"""
        # Mock repository
        mock_repo.new_instance.return_value = MagicMock()
        
        titles, categories = self.trainer_manager.prepare_training_data(
            use_real_data=False,
            use_synthetic_data=True,
            synthetic_data_limit=20
        )
        
        # Check data preparation
        self.assertGreater(len(titles), 0)
        self.assertEqual(len(titles), len(categories))
        self.assertGreater(self.trainer_manager.training_data_size, 0)
        self.assertGreater(self.trainer_manager.validation_data_size, 0)


class TestIntegration(unittest.TestCase):
    """Integration tests for the complete system"""
    
    def setUp(self):
        self.temp_dir = tempfile.mkdtemp()
        
        # Create test category files
        categories_path = os.path.join(self.temp_dir, "categories.txt")
        tags_path = os.path.join(self.temp_dir, "categories_tags.txt")
        
        with open(categories_path, 'w') as f:
            f.write("Electronics\nClothing\nBooks\n")
        
        with open(tags_path, 'w') as f:
            f.write("Electronics:\niphone\nsamsung galaxy\ntv television\n\nClothing:\nshirt\npants jeans\nshoes sneakers\n\nBooks:\nnovel fiction\ntextbook\n")
    
    def tearDown(self):
        import shutil
        shutil.rmtree(self.temp_dir)
    
    def test_rule_based_classification(self):
        """Test rule-based classification"""
        config = ClassificationConfig()
        config.categories_path = os.path.join(self.temp_dir, "categories.txt")
        config.tags_path = os.path.join(self.temp_dir, "categories_tags.txt")
        
        preprocessor = TextPreprocessor(config)
        preprocessor.load_categories()
        
        # Test exact matches
        self.assertEqual(preprocessor.predict_category_from_tags("iPhone 13 Pro"), "Electronics")
        self.assertEqual(preprocessor.predict_category_from_tags("Samsung Galaxy S21"), "Electronics")
        self.assertEqual(preprocessor.predict_category_from_tags("Cotton T-Shirt"), "Clothing")
        self.assertEqual(preprocessor.predict_category_from_tags("Fiction Novel"), "Books")
        
        # Test partial matches
        self.assertEqual(preprocessor.predict_category_from_tags("Smart TV 55 inch"), "Electronics")
        self.assertEqual(preprocessor.predict_category_from_tags("Running Shoes Nike"), "Clothing")
        
        # Test no match
        self.assertIsNone(preprocessor.predict_category_from_tags("Random Product XYZ"))


if __name__ == '__main__':
    # Run tests
    unittest.main(verbosity=2)
