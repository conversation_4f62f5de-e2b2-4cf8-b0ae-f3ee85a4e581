from datetime import datetime, timezone
import logging
import random
from fastapi import APIRouter, Response
from typing import Any, List, Optional
import json
from pydantic import BaseModel, Field

from domains.repository.eprice_repository import ElasticPriceRepository
from domains.repository.product_repository import ProductRepository


logger = logging.getLogger(__name__)

class PrettyJSONResponseV3(Response):
    media_type = "application/json"

    def render(self, content: Any) -> bytes:
        return json.dumps(content, indent=2).encode("utf-8")

# Request DTO
class PredictPriceRequestV3(BaseModel):
    asin: str = Field(..., description="Product asin")
    sku: str = Field(..., description="Product sku")
    marketplace_id: str = Field(..., description="Marketplace identifier")
    customer_id: int = Field(..., description="Customer identifier")
    customer_account_id: int = Field(..., description="Customer account identifier")
    current_price: float = Field(..., description="Current price")
    current_quantity: int = Field(..., description="Current quantity")
    
# Parameter DTO for each optimization parameter set
class OptimizationParamsV3(BaseModel):
    step_price_type: str = Field(..., description="Price step type (UP/DOWN)")
    step_price: float = Field(..., description="Price step value")
    step_point: str = Field(..., description="Step point type (VALUE/PERCENT)")
    recommended_steps: int = Field(..., description="Recommended steps")
    timestamp: datetime = Field(..., description="Timestamp of the recommendation")

# Response DTO
class PredictPriceResponseV3(BaseModel):
    success: bool = Field(True, description="Request success status")
    error: Optional[str] = Field(None, description="Error message")
    optimization: Optional[OptimizationParamsV3] = Field(None, description="Optimization parameters")

# Request DTO for batch predictions
class BatchItemRequestV3(BaseModel):
    request_id: str = Field(..., description="Request ID")
    params: PredictPriceRequestV3 = Field(..., description="List of products to predict pricing for")

class PredictPriceBatchRequestV3(BaseModel):
    items: List[BatchItemRequestV3] = Field(..., description="List of products to predict pricing for")

class BatchItemResultV3(BaseModel):
    success: bool = Field(True, description="Request success status")
    error: Optional[str] = Field(None, description="Error message")
    request_id: str = Field(..., description="Request ID")
    optimization: Optional[OptimizationParamsV3] = Field(None, description="Optimization parameters")


# Response DTO for batch predictions
class PredictPriceBatchResponseV3(BaseModel):
    success: bool = Field(True, description="Overall request success status")
    error: Optional[List[str]] = Field(None, description="Overall error message")
    results: List[BatchItemResultV3] = Field(..., description="Individual prediction results")

# Create router for elastic price endpoints
elastic_price_recommendation_router = APIRouter(tags=["elastic-price"])

def get_recommendation(customer_id: int, asin: str, sku: str, marketplace_id: str, current_price: float, current_quantity: int) -> Optional[OptimizationParamsV3]:
    # main_repo = ProductRepository.new_instance()
    # customer_id = main_repo.get_customer_id_by_amazon_customer_account_id(customer_account_id)

    # if customer_id is None:
    #     return None
    # #if

    repo = ElasticPriceRepository.new_instance()

    recommendation = repo.get_recommendation(customer_id, asin, sku, marketplace_id)

    step_price_type = 'HOLD'
    step_price = 0.0
    step_point = "VALUE"
    recommended_steps = 0
    timestamp = datetime.now(timezone.utc)

    # if recommendation is None:
    #     step_price_type = 'DOWN'
    #     step_point = "PERCENT"
    #     recommended_steps = 1
    #     step_price = random.randint(1, 3) #1-3%
    # else :
    if recommendation is not None:
        old_days = (datetime.now(timezone.utc) - recommendation.modified_at).days
        
        # if recommendation is less than 1 day old, use it
        if old_days <= 1:
            if recommendation.recommended_action == 'RAISE_PRICE':
                step_price_type = 'UP'
            elif recommendation.recommended_action == 'LOWER_PRICE':
                step_price_type = 'DOWN'

            step_point = "VALUE"
            recommended_steps = 1
            step_price = round(current_price * random.uniform(0.05, 0.1), 2) #5-10%
            timestamp = recommendation.modified_at
        elif old_days > 1 and old_days <= 3:
            step_price_type = 'DOWN'
            step_point = "PERCENT"
            recommended_steps = 1
            step_price = random.randint(1, old_days) #1-3%
    # if


    return OptimizationParamsV3(
        step_price_type= step_price_type,
        step_price=step_price,
        step_point=step_point,
        recommended_steps= recommended_steps,
        timestamp=timestamp
    )
#def


@elastic_price_recommendation_router.post(
    "/predict", 
    response_class=PrettyJSONResponseV3, 
    response_model=PredictPriceResponseV3,
    summary="Predict elastic pricing parameters",
    description="""
    Predicts optimal pricing parameters based on product information.
    
    This endpoint uses a machine learning model to generate pricing optimization 
    parameters based on the provided product details. The model analyzes the asin, sku, 
    marketplace, and current price to suggest optimal pricing strategies.
    """,
    responses={
        200: {
            "description": "Successful prediction",
            "content": {
                "application/json": {
                    "example": {
                        "success": True,
                        "error": None,
                        "optimization": {
                            "step_price_type": "UP",
                            "step_price": 5.0,
                            "step_point": "VALUE",
                            "recommended_steps": 1,
                        },
                    }
                }
            }
        },
        422: {
            "description": "Validation Error",
            "content": {
                "application/json": {
                    "example": {
                        "detail": [
                            {
                                "loc": ["body", "params", "asin"],
                                "msg": "field required",
                                "type": "value_error.missing"
                            }
                        ]
                    }
                }
            }
        },
        500: {
            "description": "Prediction Error",
            "content": {
                "application/json": {
                    "example": {
                        "success": False,
                        "error": "Failed to predict price",
                        "optimization": None
                    }
                }
            }
        }
    }
)
async def predict_price(request: PredictPriceRequestV3):
    """
    Predict optimal pricing parameters for a product.
    
    The endpoint accepts product details and returns a list of pricing optimization 
    parameters that can be applied to maximize sales and profit.
    
    Each parameter set includes:
    - step_price_type: Whether to increase (UP) or decrease (DOWN) the price
    - step_price: The amount to change the price by
    - step_point: Whether the change is a fixed value or percentage
    """
    
    recommendation = get_recommendation(request.customer_id, request.asin, request.sku, request.marketplace_id, request.current_price, request.current_quantity)

    if not recommendation:
        return PredictPriceResponseV3(success=False, error="Failed to predict price", optimization=None)
    
    return PredictPriceResponseV3(
        success=True, error="", 
        optimization=recommendation
    )
#def

@elastic_price_recommendation_router.post(
    "/predict-batch", 
    response_class=PrettyJSONResponseV3, 
    response_model=PredictPriceBatchResponseV3,
    summary="Predict elastic pricing parameters for multiple products",
    description="""
    Predicts optimal pricing parameters for multiple products in a single request.
    
    This endpoint uses a machine learning model to generate pricing optimization 
    parameters based on the provided product details. The model analyzes the asin, sku, 
    marketplace, and current price to suggest optimal 
    pricing strategies for each product in the batch.
    """,
    responses={
        200: {
            "description": "Successful batch prediction",
            "content": {
                "application/json": {
                    "example": {
                        "success": True,
                        "error": None,
                        "results": [
                            {
                                "request_id": "1", 
                                "optimization": {
                                    "step_price_type": "UP",
                                    "step_price": 5.0,
                                    "step_point": "VALUE",
                                    "recommended_steps": 1,
                                }
                            },
                            {
                                "request_id": "2",
                                "optimization": {
                                    "step_price_type": "DOWN",
                                    "step_price": 10.0,
                                    "step_point": "PERCENT",
                                    "recommended_steps": 1,
                                }
                            }
                        ]
                    }
                }
            }
        },
        422: {
            "description": "Validation Error",
            "content": {
                "application/json": {
                    "example": {
                        "detail": [
                            {
                                "loc": ["body", "items", 0, "params", "asin"],
                                "msg": "field required",
                                "type": "value_error.missing"
                            }
                        ]
                    }
                }
            }
        },
        500: {
            "description": "Prediction Error",
            "content": {
                "application/json": {
                    "example": {
                        "success": False,
                        "error": "Failed to process batch prediction",
                        "results": []
                    }
                }
            }
        }
    }
)
async def predict_price_batch(request: PredictPriceBatchRequestV3):
    """
    Predict optimal pricing parameters for multiple products in a batch.
    
    The endpoint accepts a list of product details and returns a list of pricing optimization 
    parameters for each product that can be applied to maximize sales and profit.
    
    Each parameter set includes:
    - step_price_type: Whether to increase (UP) or decrease (DOWN) the price
    - step_price: The amount to change the price by
    - step_point: Whether the change is a fixed value or percentage
    """
    
    # Process each request in the batch
    results = []
    errors = []
    for item in request.items:
        try:

            prediction = get_recommendation(
                customer_id=item.params.customer_id,
                asin=item.params.asin,
                sku=item.params.sku,
                marketplace_id=item.params.marketplace_id,
                current_price=item.params.current_price,
                current_quantity=item.params.current_quantity
            )


            if prediction is None:
                results.append(
                    BatchItemResultV3(
                        success=False,
                        error="Failed to predict price",
                        request_id=item.request_id,
                        optimization=None
                    )
                )
                continue
            
            results.append(
                BatchItemResultV3(
                    success=True,
                    error=None,
                    request_id=item.request_id,
                    optimization=prediction
                )
            )
        except Exception as e:
            logger.error(f"Error processing batch item: {str(e)}")
            results.append(
                BatchItemResultV3(
                    success=False,
                    error=f"Error processing request: {str(e)}",
                    request_id=item.request_id,
                    optimization=None
                )
            )
            errors.append(f"Error processing request: {str(e)}")
    

    # Return the batch response
    return PredictPriceBatchResponseV3(
        success=True if not errors else False,
        error=errors,
        results=results
    )
#def